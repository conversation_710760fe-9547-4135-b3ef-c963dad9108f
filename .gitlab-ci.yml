image: node:18-alpine

before_script:
  - apk add nodejs npm
  - npm install -g pnpm

after_script:
  - echo "After script section"
  - echo "For example you might do some cleanup here"

stages:
  - test
  - test2
  - build

test:
  stage: test
  script:
    - echo "$test_key"
  only:
    - main

test2:
  stage: test2
  script:
    - echo "This is test2"
  only:
    - dev

build:
  stage: build
  script:
    - pnpm install
